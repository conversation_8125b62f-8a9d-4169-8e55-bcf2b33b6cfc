import { ref, onMounted, watch, computed, reactive } from 'vue';
import { router, usePage } from '@inertiajs/vue3';
import { mergeFirstObject, parseFilterValues, removeNullValues } from '@spa/helpers';
import useConfirm from '@spa/services/useConfirm';
import { isCompositeFilterDescriptor } from '@progress/kendo-data-query';
import { useLoaderStore } from '@spa/stores/modules/global-loader';
import globalHelper from '@spa/plugins/global-helper';

import axios from 'axios';

// Define $http for API calls
const $http = {
    get: (url, config = {}) => axios.get(url, config),
    post: (url, data, config = {}) => axios.post(url, data, config),
    put: (url, data, config = {}) => axios.put(url, data, config),
    delete: (url, config = {}) => axios.delete(url, config),
};

export default function useResultImportResource(url, config = {}) {
    const $page = usePage();
    const $confirm = useConfirm();
    const $loader = useLoaderStore();

    const defaultUrl = url;
    const onlyProps = config['only'] || [];

    const state = reactive({
        filters: {},
        sortBy: 'created_at',
        dir: 'desc',
        error: null,
        selected: [],
        selectAll: false,
        imports: { data: [] },
        loading: false,
        refreshing: false,
        deleting: false,
        detailsModalVisible: false,
        deleteModalVisible: false,
        editModalVisible: false,
        selectedImport: null,
        importToDelete: null,
        importToEdit: null,
        saving: false,
        pageable: {
            totalItems: 0,
            totalPages: 0,
            currentPage: 1,
            skip: 0,
            buttonCount: 5,
            info: true,
            type: 'numeric',
            pageSizes: [10, 20, 50, 100, 150, 200],
            previousNext: true,
            pageSizeValue: 10,
        },
        uploadProgress: 0,
    });

    if (config['filters']) {
        state.filters = { ...state.filters, ...config['filters'] };
        //merge the parameters from page query
        if ($page.props && $page.props?.query) {
            state.filters = mergeFirstObject(state.filters, $page.props?.query);
        }

        let currentPage = parseInt($page.props.data?.grid?.meta?.current_page) || 1;
        let totalPages = parseInt($page.props.data?.grid?.meta?.last_page) || 0;
        let totalItems = parseInt($page.props.data?.grid?.meta?.total) || 0;
        let take = parseInt($page.props.data?.grid?.meta?.per_page) || 10;
        let skip = (currentPage - 1) * take;
        state.pageable.currentPage = currentPage;
        state.pageable.pageSizeValue = take;
        state.pageable.skip = skip;
        state.pageable.totalPages = totalPages;
        state.pageable.totalItems = totalItems;
        state.filters.take = take;
        state.filters.page = currentPage;
    }

    /**
     * Execute an action with common error handling and loading states
     * @param {string} action - Action name for logging
     * @param {Object} data - Data to send to the API
     * @param {Object} options - Additional options
     */
    const executeAction = async (action, data = {}, options = {}) => {
        const {
            url = `list`,
            method = 'get',
            reFetch = false,
            onSuccess = () => {},
            onError = () => {},
            onBefore = () => {},
            onFinish = () => {},
            showToast = false,
            params = {},
        } = options;

        try {
            onBefore();
            let response;

            const resp = await $http[method](url, data);
            response = resp.data;

            if (response.success || response.status === 200) {
                if (onSuccess) onSuccess(response);
                if (reFetch) fetch();
                if (showToast) {
                    globalHelper.methods.showPopupSuccess(
                        response.message || 'Operation completed successfully',
                        'Success'
                    );
                }
                return response;
            } else {
                if (onError) onError(response);
                if (showToast) {
                    globalHelper.methods.showPopupError(
                        response.message || 'Operation failed',
                        'Error'
                    );
                }
            }
        } catch (error) {
            onError(error);
            if (showToast) {
                globalHelper.methods.showPopupError(
                    error.response?.data?.message || 'An error occurred',
                    'Error'
                );
            }
            console.error(`[useResultImportResource] ${action} error:`, error);
        } finally {
            onFinish();
        }
    };

    /**
     * Fetch imports from the API
     * @param {Object} params - Query parameters
     * @param {boolean} showLoader - Whether to show the loader
     */
    const fetch = (params = {}, showLoader = true) => {
        params = {
            ...state.filters,
            sortBy: state.sortBy,
            dir: state.dir,
            ...params,
        };

        if (state.filters.search === '') {
            state.filters.search = null;
        }

        params = removeNullValues(params);
        console.log('Fetch params being sent:', params);

        router.get(`/${defaultUrl}/list`, params, {
            only: [...onlyProps, 'grid', 'data'],
            preserveState: true,
            onBefore: () => {
                if (showLoader) {
                    $loader.startContextLoading('grid-loader');
                }
            },
            onSuccess: (page) => {
                if (page.props.grid) {
                    state.imports = page.props.data.imports;

                    // Update pagination
                    const paginationData = page.props.grid;
                    setPagination(state, paginationData);
                }
            },
            onError: () => {
                if (showLoader) {
                    $loader.stopContextLoading('grid-loader');
                }
            },
            onFinish: () => {
                if (showLoader) {
                    $loader.stopContextLoading('grid-loader');
                }
            },
        });
    };

    /**
     * Refresh data
     */
    const refreshData = () => {
        state.refreshing = true;
        fetch({
            take: state.pageable.pageSizeValue,
            page: state.pageable.currentPage,
        });
        state.refreshing = false;
    };

    /**
     * Toggle sort direction
     */
    const toggleSortDirection = () => {
        state.dir = state.dir === 'asc' ? 'desc' : 'asc';
        fetch({ sortBy: state.sortBy, dir: state.dir });
    };

    /**
     * Handle sort change
     * @param {Array} sort - Sort configuration
     */
    const handleSort = (sort) => {
        console.log('handleSort called with:', sort);
        if (sort && sort.length > 0) {
            state.sortBy = sort[0].field;
            state.dir = sort[0].dir;
            console.log('Setting sort state:', { sortBy: state.sortBy, dir: state.dir });
            fetch({ sortBy: state.sortBy, dir: state.dir });
        }
    };

    /**
     * Handle page change
     * @param {Object} event - Page change event
     */
    const handlePageChange = (event) => {
        console.log('handlePageChange called with event:', event);

        // Handle different event structures
        let take, skip, currentPage;

        if (event.page && typeof event.page === 'object') {
            // Kendo Grid style event
            take = parseInt(event.page.take) || parseInt(event.take) || 10;
            skip = parseInt(event.page.skip) || parseInt(event.skip) || 0;
            currentPage = Math.max(1, Math.floor(skip / take) + 1);
        } else if (typeof event === 'number') {
            // Direct page number
            currentPage = Math.max(1, event);
            take = state.pageable.pageSizeValue || 10;
            skip = (currentPage - 1) * take;
        } else {
            // Fallback to original logic
            take = parseInt(event.take) || 10;
            skip = parseInt(event.skip) || 0;
            currentPage = Math.max(1, Math.floor(skip / take) + 1);
        }

        console.log('Calculated pagination:', { take, skip, currentPage });

        // Update both pageable state and filters
        state.pageable.pageSizeValue = take;
        state.pageable.currentPage = currentPage;
        state.pageable.skip = skip;

        // Update filters to ensure they're used in the fetch call
        state.filters.take = take;
        state.filters.page = currentPage;

        fetch({
            take: take,
            page: currentPage,
        });
    };

    /**
     * Handle filter change
     * @param {Object} filters - Filter values
     */
    const handleFilter = (filters) => {
        state.filters = { ...state.filters, ...filters };
        // Reset to page 1 when filters change
        state.pageable.currentPage = 1;
        state.filters.page = 1;
        fetch({
            take: state.pageable.pageSizeValue,
            page: 1,
        });
    };

    /**
     * Upload a file
     * @param {File} file - File to upload
     */
    const uploadFile = async (file) => {
        if (!file) return;

        const formData = new FormData();
        formData.append('file', file);

        return executeAction('upload', formData, {
            url: `upload`,
            method: 'post',
            reFetch: true,
            showToast: true,
            onBefore: () => {
                $loader.startContextLoading('grid-loader');
            },
            onFinish: () => {
                // $loader.stopContextLoading('grid-loader');
            },
        });
    };

    /**
     * Download sample CSV
     */
    const downloadSample = (type) => {
        window.location.href = `/spa/result-import/sample/${type}`;
    };

    /**
     * View import details
     * @param {Object} importData - Import data to view
     */
    const viewDetails = (importData) => {
        state.selectedImport = importData;
        state.detailsModalVisible = true;
    };

    /**
     * Confirm delete
     * @param {Object} importData - Import data to delete
     */
    const confirmDelete = (importData) => {
        $confirm.require({
            message: 'Are you sure you want to delete this import? This action cannot be undone.',
            header: 'Delete Import',
            icon: 'pi pi-exclamation-triangle',
            variant: 'danger',
            acceptLabel: 'Delete',
            rejectLabel: 'Cancel',
            accept: () => {
                state.importToDelete = importData;
                deleteImport();
            },
        });
    };

    /**
     * Delete import
     */
    const deleteImport = async () => {
        if (!state.importToDelete) return;

        state.deleting = true;

        await executeAction(
            'delete',
            {},
            {
                url: `${state.importToDelete.id}`,
                method: 'delete',
                reFetch: true,
                showToast: true,
                onBefore: () => {
                    $loader.startContextLoading('grid-loader');
                },
                onSuccess: () => {
                    state.deleteModalVisible = false;
                    state.importToDelete = null;
                },
                onFinish: () => {
                    state.deleting = false;
                    // $loader.stopContextLoading('grid-loader');
                },
            }
        );
    };

    /**
     * Resync import
     * @param {Object} importData - Import data to resync
     */
    const resyncImport = async (importData) => {
        await executeAction(
            'resync',
            {},
            {
                url: `${importData.id}/resync`,
                method: 'post',
                reFetch: true,
                showToast: true,
                onBefore: () => {
                    $loader.startContextLoading('grid-loader');
                },
                onFinish: () => {
                    $loader.stopContextLoading('grid-loader');
                },
            }
        );
    };

    /**
     * Edit record
     * @param {Object} importData - Import data to edit
     */
    const editRecord = (importData) => {
        state.importToEdit = importData;
        state.editModalVisible = true;
    };

    /**
     * Save edited record
     * @param {Object} data - Updated import data
     */
    const saveEditedRecord = async (data) => {
        state.saving = true;

        await executeAction(
            'update',
            { import_data: data.import_data },
            {
                url: `${data.id}`,
                method: 'put',
                reFetch: true,
                showToast: true,
                onBefore: () => {
                    $loader.startContextLoading('grid-loader');
                },
                onSuccess: () => {
                    state.editModalVisible = false;
                    state.importToEdit = null;
                },
                onFinish: () => {
                    state.saving = false;
                    $loader.stopContextLoading('grid-loader');
                },
            }
        );
    };

    return {
        state,
        fetch,
        refreshData,
        toggleSortDirection,
        handleSort,
        handlePageChange,
        handleFilter,
        uploadFile,
        downloadSample,
        viewDetails,
        confirmDelete,
        deleteImport,
        resyncImport,
        editRecord,
        saveEditedRecord,
        executeAction,
    };
}

/**
 * Helper function to set pagination
 * @param {Object} state - State object
 * @param {Object} data - Pagination data
 */
export const setPagination = (state, data) => {
    let currentPage = parseInt(data.current_page || 1);
    if (isNaN(currentPage) || currentPage < 1) {
        currentPage = 1;
    }
    let take = parseInt(data.per_page || 10);
    if (isNaN(take) || take < 1) {
        take = 10;
    }
    const skip = Math.max(0, (currentPage - 1) * take);
    const totalItems = parseInt(data.total) || 0;
    const totalPages = parseInt(data.last_page) || Math.max(1, Math.ceil(totalItems / take));

    state.pageable.totalItems = totalItems;
    state.pageable.totalPages = totalPages;
    state.pageable.currentPage = currentPage;
    state.pageable.skip = skip;
    state.pageable.pageSizeValue = take;
    state.pageable.info = true;
    state.pageable.pageSizes = getPageSizes(totalItems);
};

/**
 * Helper function to get page sizes
 * @param {number} totalItems - Total number of items
 * @returns {Array} - Array of page sizes
 */
export const getPageSizes = (totalItems) => {
    const pageSizes = [10, 20, 50, 100, 150, 200];
    const filteredSizes = pageSizes.filter((size) => size <= totalItems);
    const nextSize = pageSizes.find((size) => size > totalItems);
    if (nextSize) {
        filteredSizes.push(nextSize);
    }
    return filteredSizes.length > 0 ? filteredSizes : [10];
};

/**
 * Helper function to prepare result import grid data
 * @param {Array} data - Raw data from API
 * @returns {Array} - Formatted data for grid
 */
export const prepareResultImportGridData = (data) => {
    if (data === undefined) return [];

    const dataArray = [];
    Object.values(data).map((item) => {
        const tempObject = {
            id: item.id || '',
            import_data: item.import_data || {},
            status: item.status || 'pending',
            error_message: item.error_message || null,
            import_batch_id: item.import_batch_id || '',
            created_at: item.created_at || '',
            updated_at: item.updated_at || '',
            created_by: item.created_by || '',
            updated_by: item.updated_by || '',
            selected: false,
        };
        dataArray.push(tempObject);
    });
    return dataArray;
};
